#!/usr/bin/env python3
"""
MTProto CLI工具
用于命令行操作和Node.js桥接
"""

import sys
import asyncio
import json
import os

# 添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

from src.services.telegram_mtproto import TelegramMTProtoService

# 配置信息
API_ID = 6667234
API_HASH = "f6fd95227e50d8cd19121457f2fb9b86"

async def init_auth(phone):
    """初始化认证"""
    service = TelegramMTProtoService(API_ID, API_HASH)
    try:
        result = await service.initialize(phone)
        print(json.dumps(result, ensure_ascii=False))
    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}, ensure_ascii=False))
    finally:
        await service.close()

async def complete_auth(phone, code, password=None):
    """完成认证"""
    service = TelegramMTProtoService(API_ID, API_HASH)
    try:
        await service.initialize()
        result = await service.complete_auth(phone, code, password)
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}))
    finally:
        await service.close()

async def upload_file(file_path, chat_id, caption=None):
    """上传文件"""
    service = TelegramMTProtoService(API_ID, API_HASH)
    try:
        await service.initialize()
        result = await service.upload_file(file_path, chat_id, caption)
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}))
    finally:
        await service.close()

async def check_status():
    """检查认证状态"""
    service = TelegramMTProtoService(API_ID, API_HASH)
    try:
        await service.initialize()
        if service.is_authenticated:
            me = await service.client.get_me()
            result = {
                "authenticated": True,
                "user": {
                    "id": me.id,
                    "first_name": me.first_name,
                    "username": me.username
                }
            }
        else:
            result = {"authenticated": False}
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({"authenticated": False, "error": str(e)}))
    finally:
        await service.close()

def main():
    if len(sys.argv) < 2:
        print(json.dumps({"error": "缺少命令参数"}))
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "init":
        if len(sys.argv) < 3:
            print(json.dumps({"error": "缺少手机号参数"}))
            sys.exit(1)
        phone = sys.argv[2]
        asyncio.run(init_auth(phone))
    
    elif command == "auth":
        if len(sys.argv) < 4:
            print(json.dumps({"error": "缺少手机号或验证码参数"}))
            sys.exit(1)
        phone = sys.argv[2]
        code = sys.argv[3]
        password = sys.argv[4] if len(sys.argv) > 4 else None
        asyncio.run(complete_auth(phone, code, password))
    
    elif command == "upload":
        if len(sys.argv) < 4:
            print(json.dumps({"error": "缺少文件路径或聊天ID参数"}))
            sys.exit(1)
        file_path = sys.argv[2]
        chat_id = sys.argv[3]
        caption = sys.argv[4] if len(sys.argv) > 4 else None
        asyncio.run(upload_file(file_path, chat_id, caption))
    
    elif command == "status":
        asyncio.run(check_status())
    
    else:
        print(json.dumps({"error": f"未知命令: {command}"}))
        sys.exit(1)

if __name__ == "__main__":
    main()