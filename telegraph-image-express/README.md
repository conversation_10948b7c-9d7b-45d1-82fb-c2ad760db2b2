# 📸 Telegraph-Image Express

> 🚀 **高性能本地部署版本** - 基于 Express.js 完全重构的 Telegraph-Image 服务

[![License](https://img.shields.io/badge/license-CC0--1.0-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-%3E%3D16.0.0-brightgreen.svg)](https://nodejs.org/)
[![Express](https://img.shields.io/badge/express-4.x-lightgrey.svg)](https://expressjs.com/)

Telegraph-Image 项目的 Express.js 本地部署版本，完全重构自 Cloudflare Pages 版本，提供更强的可控性和扩展性。

## ✨ 核心特性

- 🖼️ **多媒体支持**: 图片、视频、音频、文档一站式上传
- 🔗 **智能代理**: 高效文件代理访问和 CDN 加速
- 🛡️ **访问控制**: 灵活的黑白名单管理系统
- 👨‍💼 **管理后台**: 现代化 Web 管理界面
- 🔐 **安全防护**: Basic Auth + CORS + 安全头部
- 📊 **实时监控**: 健康检查和系统状态面板
- 🚀 **高性能**: Express.js + 优化中间件
- 🌐 **跨平台**: Linux、macOS、Windows 全支持

## 🚀 快速开始

### ⚡ 一键启动（推荐）

**Linux/macOS:**
```bash
# 开发模式（自动检查依赖和配置）
./start-dev.sh

# 生产模式（支持 PM2 进程管理）
./start-prod.sh
```

**Windows:**
```batch
# 双击运行或命令行执行
start.bat
```

### 🎯 5分钟快速体验

1. **克隆并进入目录**
   ```bash
   git clone <repository-url>
   cd telegraph-image-express
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID
   ```

3. **一键启动**
   ```bash
   ./start-dev.sh  # Linux/macOS
   # 或
   start.bat       # Windows
   ```

4. **开始使用**
   - 📤 上传文件: http://localhost:3000
   - ⚙️ 管理后台: http://localhost:3000/admin.html
   - 📊 系统状态: http://localhost:3000/health

### 方法二：手动启动

#### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```bash
# 必需配置
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# 可选配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password
```

#### 2. 安装依赖

```bash
npm install
```

#### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm run prod
```

服务将在 http://localhost:3000 启动

## 📋 环境变量说明

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `TELEGRAM_BOT_TOKEN` | ✅ | - | Telegram Bot Token（从 @BotFather 获取）|
| `TELEGRAM_CHAT_ID` | ✅ | - | Telegram 频道/群组 ID（频道使用负数 ID）|
| `ADMIN_USERNAME` | ❌ | - | 管理后台用户名（未设置则无需认证）|
| `ADMIN_PASSWORD` | ❌ | - | 管理后台密码 |
| `PORT` | ❌ | 3000 | 服务端口 |
| `NODE_ENV` | ❌ | development | 运行环境 |
| `WHITELIST_MODE` | ❌ | false | 白名单模式（仅白名单文件可访问）|
| `ALLOWED_ORIGINS` | ❌ | * | CORS 允许的源（逗号分隔）|
| `MODERATE_CONTENT_API_KEY` | ❌ | - | 内容审核 API 密钥 |

## 🏗️ 项目结构

```
telegraph-image-express/
├── src/
│   ├── app.js              # Express 应用入口
│   ├── routes/             # 路由处理
│   │   ├── upload.js       # 文件上传路由
│   │   ├── file.js         # 文件获取路由
│   │   └── api/manage/     # 管理 API 路由
│   ├── services/           # 业务服务
│   │   ├── telegram.js     # Telegram API 服务
│   │   └── storage.js      # 本地存储服务
│   └── middleware/         # 中间件
│       └── auth.js         # 认证中间件
├── public/                 # 静态文件
│   ├── index.html          # 主页面
│   ├── admin.html          # 管理后台
│   └── _nuxt/              # 前端资源
├── data/                   # 数据存储
│   └── metadata.json       # 文件元数据
├── start-dev.sh            # 开发启动脚本
├── start-prod.sh           # 生产启动脚本
├── start.bat               # Windows 启动脚本
└── package.json
```

## 🔧 API 接口

### 文件操作
- `POST /upload` - 文件上传
- `GET /file/:id` - 文件获取

### 管理 API
- `GET /api/manage/list` - 文件列表
- `POST /api/manage/white/:id` - 添加白名单
- `POST /api/manage/block/:id` - 添加黑名单
- `DELETE /api/manage/delete/:id` - 删除文件
- `GET /api/manage/check` - 认证检查

### 系统监控
- `GET /health` - 健康检查

## 🛠️ 开发说明

### 启动开发服务器
```bash
npm run dev
```

### 生产部署
```bash
npm run prod
```

### 健康检查
```bash
npm run health
```

### PM2 管理（生产环境推荐）
```bash
# 安装 PM2
npm install -g pm2

# 启动
npm run prod:script

# 查看日志
npm run logs

# 重启
npm run restart

# 停止
npm run stop
```

## 🔒 安全配置

1. **认证保护**: 设置 `ADMIN_USERNAME` 和 `ADMIN_PASSWORD` 保护管理后台
2. **CORS 配置**: 通过 `ALLOWED_ORIGINS` 限制跨域访问
3. **白名单模式**: 启用 `WHITELIST_MODE` 仅允许白名单文件访问
4. **内容审核**: 配置 `MODERATE_CONTENT_API_KEY` 启用自动内容审核

## 📝 使用说明

1. **文件上传**: 访问主页 http://localhost:3000 进行文件上传
2. **管理后台**: 访问 http://localhost:3000/admin.html 管理文件
3. **健康监控**: 访问 http://localhost:3000/health 查看系统状态

## 🆚 相比 Cloudflare Pages 版本的优势

| 特性 | Cloudflare Pages | Express 版本 |
|------|------------------|--------------|
| 🏠 **部署方式** | 云端托管 | 本地自主部署 |
| 💰 **成本** | 免费额度限制 | 完全免费 |
| 🔧 **可控性** | 平台限制 | 完全自主控制 |
| 📊 **监控** | 基础监控 | 详细系统监控 |
| 🔒 **数据安全** | 第三方存储 | 本地数据控制 |
| ⚡ **性能** | 边缘计算 | 本地高性能 |
| 🛠️ **扩展性** | 平台限制 | 无限扩展 |

## 🔄 从 Cloudflare Pages 迁移

**零成本迁移，完全兼容！**
- ✅ 保持相同的 API 接口和响应格式
- ✅ 兼容原有的前端界面和用户体验
- ✅ 支持所有原有功能（上传、管理、代理等）
- ✅ 数据格式完全一致，无需转换
- ✅ 管理后台界面和操作方式不变

## ❓ 常见问题

### Q: 如何获取 Telegram Bot Token？
A:
1. 在 Telegram 中搜索 `@BotFather`
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称
4. 获得 Token 并填入 `.env` 文件

### Q: 如何获取 Telegram Chat ID？
A:
1. 创建 Telegram 频道或群组
2. 将机器人添加为管理员
3. 发送消息到频道，然后访问：
   `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. 在返回的 JSON 中找到 `chat.id`（频道 ID 为负数）

### Q: 端口被占用怎么办？
A: 修改 `.env` 文件中的 `PORT` 值，或者停止占用端口的进程

### Q: 上传失败怎么办？
A:
1. 检查 Telegram 配置是否正确
2. 确认机器人有发送消息权限
3. 检查文件大小是否超过 20MB 限制
4. 查看服务器日志获取详细错误信息

## � 故障排除

### 服务无法启动
```bash
# 检查配置
node check-deployment.js

# 查看详细错误
DEBUG=* npm start
```

### 文件上传失败
```bash
# 测试 Telegram 连接
curl -X POST "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"

# 检查服务状态
curl http://localhost:3000/health
```

### 管理后台无法访问
1. 确认服务正常运行
2. 检查防火墙设置
3. 验证认证信息是否正确

## �📄 许可证

本项目采用 CC0-1.0 许可证，详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 开发指南
```bash
# 克隆项目
git clone <repository-url>
cd telegraph-image-express

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm test
```

## 📞 支持

- 🐛 **Bug 报告**: [提交 Issue](https://github.com/cf-pages/Telegraph-Image/issues)
- 💡 **功能建议**: [功能请求](https://github.com/cf-pages/Telegraph-Image/issues)
- 📖 **文档问题**: [文档改进](https://github.com/cf-pages/Telegraph-Image/issues)
- 💬 **使用交流**: [Discussions](https://github.com/cf-pages/Telegraph-Image/discussions)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给个 Star 支持一下！**

Made with ❤️ by Telegraph-Image Community

</div>