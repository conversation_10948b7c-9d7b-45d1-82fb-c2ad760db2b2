#!/usr/bin/env python3
"""
Telegram MTProto API Service
支持大文件上传（最大4GB）和文件分割处理
"""

import os
import asyncio
import aiofiles
import math
from typing import Optional, Dict, List, Any
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError
from telethon.tl.types import DocumentAttributeFilename
from telethon.sessions import StringSession
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramMTProtoService:
    def __init__(self, api_id: int, api_hash: str, session_name: str = 'telegram_session'):
        """
        初始化MTProto服务

        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            session_name: 会话文件名
        """
        self.api_id = api_id
        self.api_hash = api_hash

        # 使用持久化会话文件路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))
        sessions_dir = os.path.join(project_root, 'sessions')

        # 创建会话目录
        os.makedirs(sessions_dir, exist_ok=True)

        # 设置会话文件完整路径
        self.session_file = os.path.join(sessions_dir, f"{session_name}.session")
        self.session_string = self._load_session_string()
        self.client = None
        self.is_authenticated = False
        self.phone_code_hash = None  # 保存验证码哈希
        
        # 文件大小限制
        self.BOT_API_LIMIT = 50 * 1024 * 1024  # 50MB
        self.MTPROTO_LIMIT = 4 * 1024 * 1024 * 1024  # 4GB
        self.CHUNK_SIZE = 3.9 * 1024 * 1024 * 1024  # 3.9GB per chunk

    def _load_session_string(self):
        """加载会话字符串"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r') as f:
                    return f.read().strip()
        except Exception as e:
            logger.warning(f"加载会话文件失败: {e}")
        return ""

    def _save_session_string(self, session_string):
        """保存会话字符串"""
        try:
            with open(self.session_file, 'w') as f:
                f.write(session_string)
            logger.info("会话已保存")
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

    def _save_auth_state(self, phone, phone_code_hash):
        """保存认证状态"""
        try:
            auth_state = {
                "phone": phone,
                "phone_code_hash": phone_code_hash,
                "timestamp": asyncio.get_event_loop().time()
            }
            auth_file = self.session_file.replace('.session', '_auth.json')
            with open(auth_file, 'w') as f:
                json.dump(auth_state, f)
            logger.info("认证状态已保存")
        except Exception as e:
            logger.error(f"保存认证状态失败: {e}")

    def _load_auth_state(self):
        """加载认证状态"""
        try:
            auth_file = self.session_file.replace('.session', '_auth.json')
            if os.path.exists(auth_file):
                with open(auth_file, 'r') as f:
                    auth_state = json.load(f)
                # 检查是否过期（10分钟）
                if asyncio.get_event_loop().time() - auth_state.get('timestamp', 0) < 600:
                    return auth_state
                else:
                    os.remove(auth_file)  # 删除过期状态
        except Exception as e:
            logger.warning(f"加载认证状态失败: {e}")
        return None
        
    async def initialize(self, phone: str = None, password: str = None):
        """
        初始化客户端并认证
        
        Args:
            phone: 手机号码（首次认证需要）
            password: 两步验证密码（如果启用）
        """
        try:
            # 使用StringSession避免文件权限问题
            session = StringSession(self.session_string)
            self.client = TelegramClient(session, self.api_id, self.api_hash)

            # 连接到Telegram
            await self.client.connect()

            # 检查是否已有会话
            if not await self.client.is_user_authorized():
                if not phone:
                    # 如果没有手机号且未认证，返回需要认证状态
                    return {"status": "not_authenticated", "message": "需要手机号码进行认证"}

                # 发送验证码
                sent_code = await self.client.send_code_request(phone)
                self.phone_code_hash = sent_code.phone_code_hash

                # 保存认证状态
                self._save_auth_state(phone, self.phone_code_hash)

                logger.info(f"验证码已发送到 {phone}")

                return {"status": "code_required", "phone": phone, "phone_code_hash": self.phone_code_hash}
            else:
                # 已认证
                pass
            
            self.is_authenticated = True
            me = await self.client.get_me()
            logger.info(f"已认证用户: {me.first_name} (@{me.username})")
            
            return {"status": "authenticated", "user": me.first_name}
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def complete_auth(self, phone: str, code: str, password: str = None):
        """
        完成认证流程

        Args:
            phone: 手机号码
            code: 验证码
            password: 两步验证密码（如果需要）
        """
        try:
            # 如果没有phone_code_hash，尝试从状态文件加载
            if not self.phone_code_hash:
                auth_state = self._load_auth_state()
                if auth_state and auth_state.get('phone') == phone:
                    self.phone_code_hash = auth_state.get('phone_code_hash')

            if not self.phone_code_hash:
                raise ValueError("Missing phone_code_hash. Please call initialize() first.")

            # 使用保存的phone_code_hash进行认证
            await self.client.sign_in(phone, code, phone_code_hash=self.phone_code_hash)
            self.is_authenticated = True

            # 保存会话字符串
            session_string = self.client.session.save()
            self._save_session_string(session_string)

            # 清理认证状态文件
            auth_file = self.session_file.replace('.session', '_auth.json')
            if os.path.exists(auth_file):
                os.remove(auth_file)

            me = await self.client.get_me()
            logger.info(f"认证成功: {me.first_name}")
            return {"status": "success", "user": me.first_name}
            
        except SessionPasswordNeededError:
            if not password:
                return {"status": "password_required"}

            await self.client.sign_in(password=password)
            self.is_authenticated = True

            # 保存会话字符串
            session_string = self.client.session.save()
            self._save_session_string(session_string)

            me = await self.client.get_me()
            logger.info(f"认证成功: {me.first_name}")
            return {"status": "success", "user": me.first_name}
            
        except Exception as e:
            logger.error(f"认证失败: {e}")
            raise
    
    async def get_file_size(self, file_path: str) -> int:
        """获取文件大小"""
        return os.path.getsize(file_path)
    
    async def upload_file(self, file_path: str, chat_id: str, caption: str = None) -> Dict[str, Any]:
        """
        智能文件上传 - 根据文件大小选择最佳上传方式
        
        Args:
            file_path: 文件路径
            chat_id: 目标聊天ID（频道用户名或ID）
            caption: 文件说明
            
        Returns:
            上传结果字典
        """
        if not self.is_authenticated:
            raise ValueError("客户端未认证，请先调用 initialize()")
        
        file_size = await self.get_file_size(file_path)
        file_name = os.path.basename(file_path)
        
        logger.info(f"准备上传文件: {file_name} ({file_size / 1024 / 1024:.2f} MB)")
        
        try:
            if file_size <= self.MTPROTO_LIMIT:
                # 直接上传（4GB以内）
                return await self._upload_single_file(file_path, chat_id, caption)
            else:
                # 文件分割上传（超过4GB）
                return await self._upload_split_file(file_path, chat_id, caption)
                
        except Exception as e:
            logger.error(f"上传失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_name": file_name,
                "file_size": file_size
            }
    
    async def _upload_single_file(self, file_path: str, chat_id: str, caption: str = None) -> Dict[str, Any]:
        """上传单个文件"""
        file_name = os.path.basename(file_path)
        file_size = await self.get_file_size(file_path)
        
        # 添加文件名属性
        attributes = [DocumentAttributeFilename(file_name=file_name)]
        
        # 上传文件
        message = await self.client.send_file(
            chat_id,
            file_path,
            caption=caption or f"📁 {file_name}",
            attributes=attributes,
            progress_callback=self._upload_progress_callback
        )
        
        # 获取文件ID
        file_id = None
        if message.document:
            file_id = message.document.id
        
        logger.info(f"文件上传成功: {file_name}")
        
        return {
            "success": True,
            "file_name": file_name,
            "file_size": file_size,
            "file_id": file_id,
            "message_id": message.id,
            "upload_type": "single",
            "telegram_url": f"https://t.me/c/{str(chat_id).replace('-100', '')}/{message.id}"
        }
    
    async def _upload_split_file(self, file_path: str, chat_id: str, caption: str = None) -> Dict[str, Any]:
        """分割并上传大文件"""
        file_name = os.path.basename(file_path)
        file_size = await self.get_file_size(file_path)
        
        # 计算分割数量
        chunk_count = math.ceil(file_size / self.CHUNK_SIZE)
        logger.info(f"文件将分割为 {chunk_count} 个部分")
        
        # 创建临时目录
        temp_dir = f"/tmp/split_{file_name}_{asyncio.get_event_loop().time()}"
        os.makedirs(temp_dir, exist_ok=True)
        
        try:
            # 分割文件
            chunk_files = await self._split_file(file_path, temp_dir, self.CHUNK_SIZE)
            
            # 上传所有分片
            upload_results = []
            for i, chunk_file in enumerate(chunk_files):
                chunk_caption = f"📦 {file_name} - Part {i+1}/{len(chunk_files)}"
                if i == 0 and caption:
                    chunk_caption += f"\n\n{caption}"
                
                result = await self._upload_single_file(chunk_file, chat_id, chunk_caption)
                upload_results.append(result)
                
                logger.info(f"已上传分片 {i+1}/{len(chunk_files)}")
            
            # 创建合并信息
            merge_info = {
                "original_name": file_name,
                "original_size": file_size,
                "total_parts": len(chunk_files),
                "parts": upload_results
            }
            
            # 上传合并信息文件
            merge_info_path = os.path.join(temp_dir, f"{file_name}.merge_info.json")
            async with aiofiles.open(merge_info_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(merge_info, indent=2, ensure_ascii=False))
            
            merge_result = await self._upload_single_file(
                merge_info_path, 
                chat_id, 
                f"🔗 合并信息: {file_name}"
            )
            
            return {
                "success": True,
                "file_name": file_name,
                "file_size": file_size,
                "upload_type": "split",
                "total_parts": len(chunk_files),
                "parts": upload_results,
                "merge_info": merge_result
            }
            
        finally:
            # 清理临时文件
            await self._cleanup_temp_files(temp_dir)
    
    async def _split_file(self, file_path: str, output_dir: str, chunk_size: int) -> List[str]:
        """分割文件"""
        chunk_files = []
        file_name = os.path.basename(file_path)
        
        async with aiofiles.open(file_path, 'rb') as input_file:
            chunk_index = 0
            
            while True:
                chunk_data = await input_file.read(chunk_size)
                if not chunk_data:
                    break
                
                chunk_filename = f"{file_name}.part{chunk_index:03d}"
                chunk_path = os.path.join(output_dir, chunk_filename)
                
                async with aiofiles.open(chunk_path, 'wb') as chunk_file:
                    await chunk_file.write(chunk_data)
                
                chunk_files.append(chunk_path)
                chunk_index += 1
                
                logger.info(f"创建分片: {chunk_filename} ({len(chunk_data) / 1024 / 1024:.2f} MB)")
        
        return chunk_files
    
    async def _cleanup_temp_files(self, temp_dir: str):
        """清理临时文件"""
        try:
            import shutil
            shutil.rmtree(temp_dir)
            logger.info(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")
    
    def _upload_progress_callback(self, current: int, total: int):
        """上传进度回调"""
        progress = (current / total) * 100
        if progress % 10 < 1:  # 每10%显示一次
            logger.info(f"上传进度: {progress:.1f}% ({current}/{total} bytes)")
    
    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.disconnect()
            logger.info("MTProto客户端已断开连接")

# 使用示例
async def main():
    # 配置信息
    API_ID = 6667234
    API_HASH = "f6fd95227e50d8cd19121457f2fb9b86"
    PHONE = "+your_phone_number"  # 替换为您的手机号
    CHAT_ID = "@your_channel"     # 替换为您的频道
    
    # 创建服务实例
    service = TelegramMTProtoService(API_ID, API_HASH)
    
    try:
        # 初始化并认证
        result = await service.initialize(PHONE)
        print(f"初始化结果: {result}")
        
        # 如果需要验证码，在这里处理
        if result.get("status") == "code_required":
            code = input("请输入验证码: ")
            auth_result = await service.complete_auth(PHONE, code)
            print(f"认证结果: {auth_result}")
        
        # 上传文件示例
        # file_path = "/path/to/your/large/file.zip"
        # upload_result = await service.upload_file(file_path, CHAT_ID)
        # print(f"上传结果: {upload_result}")
        
    finally:
        await service.close()

if __name__ == "__main__":
    asyncio.run(main())