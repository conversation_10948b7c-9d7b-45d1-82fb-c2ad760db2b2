/**
 * MTProto Bridge Service
 * Node.js与Python MTProto服务的桥接层
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');

class MTProtoBridgeService {
    constructor() {
        this.pythonPath = path.join(__dirname, '../../venv_mtproto/bin/python');
        this.scriptPath = path.join(__dirname, 'telegram_mtproto.py');
        this.isAuthenticated = false;
        this.sessionFile = path.join(__dirname, '../../telegram_session.session');
    }

    /**
     * 检查是否已认证
     */
    async checkAuthentication() {
        try {
            // 检查会话文件是否存在
            const sessionExists = await fs.pathExists(this.sessionFile);
            this.isAuthenticated = sessionExists;
            return this.isAuthenticated;
        } catch (error) {
            console.error('检查认证状态失败:', error);
            return false;
        }
    }

    /**
     * 执行Python脚本
     */
    async executePythonScript(command, args = []) {
        return new Promise((resolve, reject) => {
            const pythonProcess = spawn(this.pythonPath, [this.scriptPath, command, ...args]);
            
            let stdout = '';
            let stderr = '';

            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            pythonProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const result = JSON.parse(stdout);
                        resolve(result);
                    } catch (error) {
                        resolve({ success: true, output: stdout });
                    }
                } else {
                    reject(new Error(`Python脚本执行失败: ${stderr || stdout}`));
                }
            });

            pythonProcess.on('error', (error) => {
                reject(new Error(`启动Python进程失败: ${error.message}`));
            });
        });
    }

    /**
     * 初始化MTProto认证
     */
    async initializeAuth(phone) {
        try {
            const result = await this.executePythonScript('init', [phone]);
            return result;
        } catch (error) {
            console.error('初始化认证失败:', error);
            throw error;
        }
    }

    /**
     * 完成认证（提供验证码）
     */
    async completeAuth(phone, code, password = null) {
        try {
            const args = [phone, code];
            if (password) {
                args.push(password);
            }
            
            const result = await this.executePythonScript('auth', args);
            if (result.success) {
                this.isAuthenticated = true;
            }
            return result;
        } catch (error) {
            console.error('完成认证失败:', error);
            throw error;
        }
    }

    /**
     * 上传大文件
     */
    async uploadLargeFile(filePath, chatId, caption = null) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('未认证，请先完成Telegram认证');
            }

            // 检查文件是否存在
            if (!await fs.pathExists(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }

            const args = [filePath, chatId];
            if (caption) {
                args.push(caption);
            }

            console.log(`开始上传大文件: ${filePath}`);
            const result = await this.executePythonScript('upload', args);
            
            return result;
        } catch (error) {
            console.error('上传大文件失败:', error);
            throw error;
        }
    }

    /**
     * 获取文件信息
     */
    async getFileInfo(filePath) {
        try {
            const stats = await fs.stat(filePath);
            return {
                size: stats.size,
                sizeFormatted: this.formatFileSize(stats.size),
                canUseBotAPI: stats.size <= 50 * 1024 * 1024,
                canUseMTProto: stats.size <= 4 * 1024 * 1024 * 1024,
                needsSplit: stats.size > 4 * 1024 * 1024 * 1024
            };
        } catch (error) {
            throw new Error(`获取文件信息失败: ${error.message}`);
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 Bytes';
        
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * 智能上传决策
     */
    async smartUpload(filePath, chatId, caption = null) {
        try {
            const fileInfo = await this.getFileInfo(filePath);
            
            console.log(`文件大小: ${fileInfo.sizeFormatted}`);
            
            if (fileInfo.canUseBotAPI) {
                console.log('使用Bot API上传（文件小于50MB）');
                // 返回标识，让调用方使用Bot API
                return {
                    useMethod: 'bot_api',
                    fileInfo: fileInfo,
                    reason: '文件小于50MB，建议使用Bot API'
                };
            } else {
                console.log('使用MTProto API上传（文件大于50MB）');
                return await this.uploadLargeFile(filePath, chatId, caption);
            }
        } catch (error) {
            console.error('智能上传失败:', error);
            throw error;
        }
    }
}

module.exports = MTProtoBridgeService;