const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const MTProtoBridgeService = require('../services/mtproto_bridge');
const TelegramService = require('../services/telegram');
const StorageService = require('../services/storage');

const router = express.Router();

// 配置multer用于大文件上传（临时存储）
const uploadLarge = multer({
    storage: multer.diskStorage({
        destination: function (req, file, cb) {
            const uploadDir = path.join(__dirname, '../../temp_uploads');
            fs.ensureDirSync(uploadDir);
            cb(null, uploadDir);
        },
        filename: function (req, file, cb) {
            // 使用时间戳避免文件名冲突
            const timestamp = Date.now();
            const ext = path.extname(file.originalname);
            const name = path.basename(file.originalname, ext);
            cb(null, `${name}_${timestamp}${ext}`);
        }
    }),
    limits: {
        fileSize: 10 * 1024 * 1024 * 1024, // 10GB临时限制
        files: 1
    }
});

// 初始化服务
const mtprotoService = new MTProtoBridgeService();
const telegramService = new TelegramService();
const storageService = new StorageService();

/**
 * POST /upload/large - 大文件上传接口
 */
router.post('/', uploadLarge.single('file'), async (req, res) => {
    let tempFilePath = null;
    
    try {
        // 验证上传文件
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        tempFilePath = req.file.path;
        const originalName = req.file.originalname;
        const fileSize = req.file.size;

        console.log(`收到大文件上传请求: ${originalName} (${fileSize} bytes)`);

        // 验证环境变量
        const chatId = process.env.TELEGRAM_CHAT_ID;
        if (!chatId) {
            return res.status(500).json({ 
                error: 'Server configuration error: Missing Telegram chat ID' 
            });
        }

        // 检查MTProto认证状态
        const isAuthenticated = await mtprotoService.checkAuthentication();
        if (!isAuthenticated) {
            return res.status(400).json({
                error: 'MTProto not authenticated. Please complete Telegram authentication first.',
                needsAuth: true
            });
        }

        // 获取文件信息并决定上传方式
        const fileInfo = await mtprotoService.getFileInfo(tempFilePath);
        
        let uploadResult;
        
        if (fileInfo.canUseBotAPI) {
            // 使用Bot API上传小文件
            console.log('使用Bot API上传文件');
            
            const botToken = process.env.TELEGRAM_BOT_TOKEN;
            if (!botToken) {
                return res.status(500).json({ 
                    error: 'Server configuration error: Missing Telegram bot token' 
                });
            }

            // 读取文件到内存
            const fileBuffer = await fs.readFile(tempFilePath);
            const mimeType = req.file.mimetype;

            const botUploadResult = await telegramService.uploadFile(
                fileBuffer,
                originalName,
                mimeType,
                chatId,
                botToken
            );

            if (!botUploadResult.success) {
                throw new Error(botUploadResult.error || 'Bot API upload failed');
            }

            const fileId = telegramService.getFileId(botUploadResult.data);
            if (!fileId) {
                throw new Error('Failed to get file ID from upload response');
            }

            uploadResult = {
                success: true,
                method: 'bot_api',
                file_id: fileId,
                file_name: originalName,
                file_size: fileSize,
                telegram_url: `https://t.me/c/${chatId.replace('-100', '')}/${botUploadResult.data.result.message_id}`
            };

        } else {
            // 使用MTProto API上传大文件
            console.log('使用MTProto API上传大文件');
            
            const caption = `📁 ${originalName}\n📊 Size: ${fileInfo.sizeFormatted}`;
            uploadResult = await mtprotoService.uploadLargeFile(tempFilePath, chatId, caption);
            
            if (!uploadResult.success) {
                throw new Error(uploadResult.error || 'MTProto upload failed');
            }

            uploadResult.method = 'mtproto';
        }

        // 保存文件元数据
        const fileKey = uploadResult.file_id || `mtproto_${Date.now()}`;
        const fileExtension = path.extname(originalName).toLowerCase();
        const storageKey = `${fileKey}${fileExtension}`;

        try {
            await storageService.put(storageKey, "", {
                metadata: {
                    TimeStamp: Date.now(),
                    ListType: "None",
                    Label: "None",
                    liked: false,
                    fileName: originalName,
                    fileSize: fileSize,
                    uploadMethod: uploadResult.method,
                    telegramUrl: uploadResult.telegram_url,
                    ...(uploadResult.upload_type === 'split' && {
                        splitUpload: true,
                        totalParts: uploadResult.total_parts
                    })
                }
            });
            
            console.log(`元数据已保存: ${storageKey}`);
        } catch (storageError) {
            console.error('保存元数据失败:', storageError);
            // 不影响主要上传流程
        }

        // 返回成功响应
        const response = {
            success: true,
            file_name: originalName,
            file_size: fileSize,
            file_size_formatted: fileInfo.sizeFormatted,
            upload_method: uploadResult.method,
            src: `/file/${storageKey}`,
            telegram_url: uploadResult.telegram_url,
            ...(uploadResult.upload_type === 'split' && {
                split_upload: true,
                total_parts: uploadResult.total_parts,
                parts_info: uploadResult.parts
            })
        };

        console.log(`大文件上传成功: ${originalName}`);
        return res.status(200).json(response);

    } catch (error) {
        console.error('大文件上传失败:', error);
        
        return res.status(500).json({
            error: error.message || 'Large file upload failed',
            details: error.stack
        });
        
    } finally {
        // 清理临时文件
        if (tempFilePath) {
            try {
                await fs.remove(tempFilePath);
                console.log(`临时文件已清理: ${tempFilePath}`);
            } catch (cleanupError) {
                console.error('清理临时文件失败:', cleanupError);
            }
        }
    }
});

/**
 * POST /upload/large/auth/init - 初始化MTProto认证
 */
router.post('/auth/init', async (req, res) => {
    try {
        const { phone } = req.body;
        
        if (!phone) {
            return res.status(400).json({ error: 'Phone number is required' });
        }

        const result = await mtprotoService.initializeAuth(phone);
        return res.status(200).json(result);
        
    } catch (error) {
        console.error('初始化认证失败:', error);
        return res.status(500).json({
            error: error.message || 'Authentication initialization failed'
        });
    }
});

/**
 * POST /upload/large/auth/complete - 完成MTProto认证
 */
router.post('/auth/complete', async (req, res) => {
    try {
        const { phone, code, password } = req.body;
        
        if (!phone || !code) {
            return res.status(400).json({ error: 'Phone number and code are required' });
        }

        const result = await mtprotoService.completeAuth(phone, code, password);
        return res.status(200).json(result);
        
    } catch (error) {
        console.error('完成认证失败:', error);
        return res.status(500).json({
            error: error.message || 'Authentication completion failed'
        });
    }
});

/**
 * GET /upload/large/status - 检查MTProto认证状态
 */
router.get('/status', async (req, res) => {
    try {
        const isAuthenticated = await mtprotoService.checkAuthentication();
        
        return res.status(200).json({
            authenticated: isAuthenticated,
            message: isAuthenticated ? 'MTProto is authenticated' : 'MTProto authentication required'
        });
        
    } catch (error) {
        console.error('检查认证状态失败:', error);
        return res.status(500).json({
            error: error.message || 'Status check failed'
        });
    }
});

module.exports = router;