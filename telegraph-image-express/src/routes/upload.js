const express = require('express');
const multer = require('multer');
const TelegramService = require('../services/telegram');
const StorageService = require('../services/storage');

const router = express.Router();

// 文件类型验证函数（已放宽，允许所有类型）
const fileFilter = (req, file, cb) => {
    // 允许所有文件类型
    cb(null, true);
};

// 配置multer用于单文件上传
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 500 * 1024 * 1024, // 500MB限制（大幅放宽）
        files: 1 // 一次只能上传一个文件
    },
    fileFilter: fileFilter
});

// 配置multer用于批量文件上传
const uploadMultiple = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 500 * 1024 * 1024, // 每个文件500MB限制
        files: 100 // 最多同时上传100个文件
    },
    fileFilter: fileFilter
});

// 初始化服务
const telegramService = new TelegramService();
const storageService = new StorageService();

/**
 * POST /upload - 文件上传接口
 */
router.post('/', (req, res, next) => {
    upload.single('file')(req, res, (err) => {
        if (err) {
            console.error('Multer error:', err);
            
            if (err instanceof multer.MulterError) {
                if (err.code === 'LIMIT_FILE_SIZE') {
                    return res.status(400).json({
                        error: 'File too large. Maximum size is 500MB.'
                    });
                }
                if (err.code === 'LIMIT_FILE_COUNT') {
                    return res.status(400).json({ 
                        error: 'Too many files. Only one file allowed per upload.' 
                    });
                }
            }
            
            if (err.message.includes('not allowed')) {
                return res.status(400).json({ 
                    error: err.message 
                });
            }
            
            return res.status(500).json({ 
                error: 'File upload error: ' + err.message 
            });
        }
        
        next();
    });
}, async (req, res) => {
    try {
        // 验证上传文件存在
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const uploadFile = req.file;
        const fileName = uploadFile.originalname;
        const fileExtension = fileName.split('.').pop().toLowerCase();
        const mimeType = uploadFile.mimetype;

        // 验证环境变量
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        const chatId = process.env.TELEGRAM_CHAT_ID;

        if (!botToken || !chatId) {
            return res.status(500).json({ 
                error: 'Server configuration error: Missing Telegram credentials' 
            });
        }        // 上传文件到Telegram
        console.log(`Uploading file: ${fileName} (${mimeType}, ${uploadFile.size} bytes)`);
        
        const uploadResult = await telegramService.uploadFile(
            uploadFile.buffer,
            fileName,
            mimeType,
            chatId,
            botToken
        );

        if (!uploadResult.success) {
            console.error('Telegram upload failed:', uploadResult.error);
            return res.status(500).json({ 
                error: uploadResult.error || 'Failed to upload to Telegram' 
            });
        }

        // 从Telegram响应中提取file_id
        const fileId = telegramService.getFileId(uploadResult.data);
        
        if (!fileId) {
            console.error('Failed to get file ID from Telegram response');
            return res.status(500).json({ 
                error: 'Failed to get file ID from upload response' 
            });
        }

        console.log(`File uploaded successfully, file_id: ${fileId}`);

        // 保存文件元数据到本地存储
        const fileKey = `${fileId}.${fileExtension}`;
        
        try {
            await storageService.put(fileKey, "", {
                metadata: {
                    TimeStamp: Date.now(),
                    ListType: "None",
                    Label: "None",
                    liked: false,
                    fileName: fileName,
                    fileSize: uploadFile.size,
                }
            });
            
            console.log(`Metadata saved for: ${fileKey}`);
        } catch (storageError) {
            console.error('Failed to save metadata:', storageError);
            // 即使元数据保存失败，也返回成功响应，因为文件已经上传到Telegram
        }        // 返回成功响应（与原项目格式完全一致）
        return res.status(200).json([{ 
            'src': `/file/${fileKey}` 
        }]);

    } catch (error) {
        console.error('Upload error:', error);
        
        // 处理multer错误
        if (error instanceof multer.MulterError) {
            if (error.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({
                    error: 'File too large. Maximum size is 500MB.'
                });
            }
            if (error.code === 'LIMIT_FILE_COUNT') {
                return res.status(400).json({
                    error: 'Too many files. Only one file allowed per upload.'
                });
            }
        }
        
        // 处理文件类型错误
        if (error.message.includes('not allowed')) {
            return res.status(400).json({ 
                error: error.message 
            });
        }
        
        // 通用错误响应
        return res.status(500).json({ 
            error: error.message || 'Internal server error' 
        });
    }
});

/**
 * POST /upload/batch - 批量文件上传接口
 */
router.post('/batch', (req, res, next) => {
    uploadMultiple.array('files', 10)(req, res, (err) => {
        if (err) {
            console.error('Multer batch error:', err);

            if (err instanceof multer.MulterError) {
                if (err.code === 'LIMIT_FILE_SIZE') {
                    return res.status(400).json({
                        error: 'One or more files are too large. Maximum size is 500MB per file.'
                    });
                }
                if (err.code === 'LIMIT_FILE_COUNT') {
                    return res.status(400).json({
                        error: 'Too many files. Maximum 100 files allowed per batch upload.'
                    });
                }
            }

            if (err.message.includes('not allowed')) {
                return res.status(400).json({
                    error: err.message
                });
            }

            return res.status(500).json({
                error: 'Batch upload error: ' + err.message
            });
        }

        next();
    });
}, async (req, res) => {
    try {
        // 验证上传文件存在
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No files uploaded' });
        }

        // 验证环境变量
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        const chatId = process.env.TELEGRAM_CHAT_ID;

        if (!botToken || !chatId) {
            return res.status(500).json({
                error: 'Server configuration error: Missing Telegram credentials'
            });
        }

        console.log(`Batch uploading ${req.files.length} files`);

        const results = [];
        const errors = [];

        // 并发上传所有文件
        const uploadPromises = req.files.map(async (uploadFile, index) => {
            try {
                const fileName = uploadFile.originalname;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                const mimeType = uploadFile.mimetype;

                console.log(`[${index + 1}/${req.files.length}] Uploading: ${fileName} (${mimeType}, ${uploadFile.size} bytes)`);

                // 上传文件到Telegram
                const uploadResult = await telegramService.uploadFile(
                    uploadFile.buffer,
                    fileName,
                    mimeType,
                    chatId,
                    botToken
                );

                if (!uploadResult.success) {
                    throw new Error(uploadResult.error || 'Failed to upload to Telegram');
                }

                // 从Telegram响应中提取file_id
                const fileId = telegramService.getFileId(uploadResult.data);

                if (!fileId) {
                    throw new Error('Failed to get file ID from upload response');
                }

                console.log(`[${index + 1}/${req.files.length}] Upload successful, file_id: ${fileId}`);

                // 保存文件元数据到本地存储
                const fileKey = `${fileId}.${fileExtension}`;

                try {
                    await storageService.put(fileKey, "", {
                        metadata: {
                            TimeStamp: Date.now(),
                            ListType: "None",
                            Label: "None",
                            liked: false,
                            fileName: fileName,
                            fileSize: uploadFile.size,
                        }
                    });

                    console.log(`[${index + 1}/${req.files.length}] Metadata saved for: ${fileKey}`);
                } catch (storageError) {
                    console.error(`[${index + 1}/${req.files.length}] Failed to save metadata:`, storageError);
                    // 即使元数据保存失败，也继续处理，因为文件已经上传到Telegram
                }

                return {
                    index: index,
                    success: true,
                    src: `/file/${fileKey}`,
                    filename: fileName,
                    size: uploadFile.size
                };

            } catch (error) {
                console.error(`[${index + 1}/${req.files.length}] Upload failed:`, error);
                return {
                    index: index,
                    success: false,
                    error: error.message,
                    filename: uploadFile.originalname
                };
            }
        });

        // 等待所有上传完成
        const uploadResults = await Promise.all(uploadPromises);

        // 分离成功和失败的结果
        uploadResults.forEach(result => {
            if (result.success) {
                results.push({
                    src: result.src,
                    filename: result.filename,
                    size: result.size
                });
            } else {
                errors.push({
                    filename: result.filename,
                    error: result.error
                });
            }
        });

        console.log(`Batch upload completed: ${results.length} successful, ${errors.length} failed`);

        // 返回批量上传结果
        return res.status(200).json({
            success: results.length,
            failed: errors.length,
            total: req.files.length,
            results: results,
            errors: errors.length > 0 ? errors : undefined
        });

    } catch (error) {
        console.error('Batch upload error:', error);
        return res.status(500).json({
            error: error.message || 'Internal server error'
        });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('Upload route error:', error);
    
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                error: 'File too large. Maximum size is 500MB.'
            });
        }
        return res.status(400).json({
            error: 'File upload error: ' + error.message
        });
    }
    
    res.status(500).json({ 
        error: 'Internal server error' 
    });
});

module.exports = router;