<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大文件上传 - Telegraph Image</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .auth-section, .upload-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .file-drop-zone {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .file-drop-zone:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .file-drop-zone.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s;
        }

        .hidden {
            display: none;
        }

        .result-link {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .result-link a {
            color: #28a745;
            text-decoration: none;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 大文件上传</h1>
            <p>支持最大4GB文件上传，超大文件自动分割</p>
        </div>

        <!-- 认证状态 -->
        <div id="authStatus" class="status info">
            <span id="authStatusText">检查认证状态中...</span>
        </div>

        <!-- 认证部分 -->
        <div id="authSection" class="auth-section hidden">
            <h3 class="section-title">📱 Telegram认证</h3>
            
            <div id="phoneStep">
                <div class="form-group">
                    <label for="phoneNumber">手机号码（包含国家代码）:</label>
                    <input type="tel" id="phoneNumber" placeholder="+86 138 0013 8000" required>
                </div>
                <button class="btn" onclick="initAuth()">发送验证码</button>
            </div>

            <div id="codeStep" class="hidden">
                <div class="form-group">
                    <label for="verificationCode">验证码:</label>
                    <input type="text" id="verificationCode" placeholder="12345" required>
                </div>
                <div class="form-group">
                    <label for="password">两步验证密码（如果启用）:</label>
                    <input type="password" id="password" placeholder="可选">
                </div>
                <button class="btn" onclick="completeAuth()">完成认证</button>
            </div>
        </div>

        <!-- 上传部分 -->
        <div id="uploadSection" class="upload-section hidden">
            <h3 class="section-title">📁 文件上传</h3>
            
            <div class="file-drop-zone" id="dropZone" onclick="document.getElementById('fileInput').click()">
                <p>📎 点击选择文件或拖拽文件到此处</p>
                <p style="color: #999; margin-top: 10px;">支持任意格式，最大4GB</p>
            </div>
            
            <input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(event)">
            
            <div id="fileInfo" class="file-info hidden">
                <h4>文件信息:</h4>
                <p><strong>文件名:</strong> <span id="fileName"></span></p>
                <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
                <p><strong>上传方式:</strong> <span id="uploadMethod"></span></p>
            </div>

            <div id="uploadProgress" class="hidden">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">准备上传...</p>
            </div>

            <button class="btn" id="uploadBtn" onclick="uploadFile()" disabled>开始上传</button>
        </div>

        <!-- 上传结果 -->
        <div id="uploadResult" class="hidden">
            <div class="result-link">
                <h4>✅ 上传成功!</h4>
                <p><strong>访问链接:</strong> <a id="resultLink" href="#" target="_blank"></a></p>
                <p id="uploadDetails"></p>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let authPhone = null;

        // 页面加载时检查认证状态
        window.onload = function() {
            checkAuthStatus();
        };

        // 检查认证状态
        async function checkAuthStatus() {
            try {
                const response = await fetch('/upload/large/status');
                const result = await response.json();
                
                if (result.authenticated) {
                    showStatus('success', '✅ 已认证，可以开始上传大文件');
                    showUploadSection();
                } else {
                    showStatus('info', '⚠️ 需要先完成Telegram认证');
                    showAuthSection();
                }
            } catch (error) {
                showStatus('error', '❌ 检查认证状态失败: ' + error.message);
                showAuthSection();
            }
        }

        // 初始化认证
        async function initAuth() {
            const phone = document.getElementById('phoneNumber').value.trim();
            if (!phone) {
                alert('请输入手机号码');
                return;
            }

            authPhone = phone;
            
            try {
                showStatus('info', '📱 正在发送验证码...');
                
                const response = await fetch('/upload/large/auth/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ phone })
                });

                const result = await response.json();
                
                if (result.status === 'code_required') {
                    showStatus('success', '✅ 验证码已发送，请查看Telegram消息');
                    document.getElementById('phoneStep').classList.add('hidden');
                    document.getElementById('codeStep').classList.remove('hidden');
                } else if (result.status === 'authenticated') {
                    showStatus('success', '✅ 认证成功!');
                    showUploadSection();
                } else {
                    showStatus('error', '❌ 发送验证码失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                showStatus('error', '❌ 网络错误: ' + error.message);
            }
        }

        // 完成认证
        async function completeAuth() {
            const code = document.getElementById('verificationCode').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!code) {
                alert('请输入验证码');
                return;
            }

            try {
                showStatus('info', '🔐 正在验证...');
                
                const response = await fetch('/upload/large/auth/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        phone: authPhone, 
                        code, 
                        password: password || null 
                    })
                });

                const result = await response.json();
                
                if (result.status === 'success') {
                    showStatus('success', `✅ 认证成功! 欢迎 ${result.user}`);
                    showUploadSection();
                } else if (result.status === 'password_required') {
                    showStatus('error', '❌ 需要两步验证密码，请输入密码后重试');
                } else {
                    showStatus('error', '❌ 认证失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                showStatus('error', '❌ 网络错误: ' + error.message);
            }
        }

        // 文件拖拽处理
        const dropZone = document.getElementById('dropZone');
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择处理
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理选中的文件
        function handleFile(file) {
            selectedFile = file;
            
            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            
            // 判断上传方式
            let method = '';
            if (file.size <= 50 * 1024 * 1024) {
                method = 'Bot API (小于50MB)';
            } else if (file.size <= 4 * 1024 * 1024 * 1024) {
                method = 'MTProto API (50MB-4GB)';
            } else {
                method = 'MTProto API + 文件分割 (超过4GB)';
            }
            
            document.getElementById('uploadMethod').textContent = method;
            document.getElementById('fileInfo').classList.remove('hidden');
            document.getElementById('uploadBtn').disabled = false;
        }

        // 上传文件
        async function uploadFile() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            try {
                document.getElementById('uploadBtn').disabled = true;
                document.getElementById('uploadProgress').classList.remove('hidden');
                
                updateProgress(0, '开始上传...');

                const response = await fetch('/upload/large', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    updateProgress(100, '上传完成!');
                    showUploadResult(result);
                } else {
                    throw new Error(result.error || '上传失败');
                }
            } catch (error) {
                showStatus('error', '❌ 上传失败: ' + error.message);
                document.getElementById('uploadProgress').classList.add('hidden');
            } finally {
                document.getElementById('uploadBtn').disabled = false;
            }
        }

        // 显示上传结果
        function showUploadResult(result) {
            const resultLink = document.getElementById('resultLink');
            resultLink.href = result.src;
            resultLink.textContent = result.src;
            
            let details = `文件名: ${result.file_name}\n`;
            details += `大小: ${result.file_size_formatted}\n`;
            details += `上传方式: ${result.upload_method}`;
            
            if (result.split_upload) {
                details += `\n分片数量: ${result.total_parts}`;
            }
            
            document.getElementById('uploadDetails').textContent = details;
            document.getElementById('uploadResult').classList.remove('hidden');
        }

        // 更新进度
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        // 显示状态消息
        function showStatus(type, message) {
            const statusEl = document.getElementById('authStatus');
            statusEl.className = `status ${type}`;
            document.getElementById('authStatusText').textContent = message;
        }

        // 显示认证部分
        function showAuthSection() {
            document.getElementById('authSection').classList.remove('hidden');
            document.getElementById('uploadSection').classList.add('hidden');
        }

        // 显示上传部分
        function showUploadSection() {
            document.getElementById('authSection').classList.add('hidden');
            document.getElementById('uploadSection').classList.remove('hidden');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            if (bytes === 0) return '0 Bytes';
            
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
    </script>
</body>
</html>