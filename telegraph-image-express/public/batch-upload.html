<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量上传 - Telegraph-Image Express</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .upload-section {
            padding: 40px;
        }
        
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }
        
        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #333;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #666;
            font-size: 0.9em;
        }
        
        #fileInput {
            display: none;
        }
        
        .file-list {
            margin-top: 30px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 600;
            color: #333;
        }
        
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .file-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .status-waiting {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-uploading {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            width: 100%;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .result-link:hover {
            text-decoration: underline;
        }
        
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📤 批量上传</h1>
            <p>一次上传多个文件到 Telegraph-Image</p>
        </div>
        
        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择文件或拖拽到此处</div>
                <div class="upload-hint">支持同时上传最多10个文件，每个文件最大20MB</div>
                <input type="file" id="fileInput" multiple accept="image/*,video/*,audio/*,.pdf,.txt,.zip">
            </div>
            
            <div class="file-list" id="fileList"></div>
            
            <button class="upload-btn" id="uploadBtn" style="display: none;">开始批量上传</button>
            
            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="results" id="results"></div>
            
            <a href="/" class="back-link">← 返回主页</a>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const results = document.getElementById('results');
        
        let selectedFiles = [];
        
        // 点击上传区域选择文件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        // 处理选择的文件
        function handleFiles(files) {
            selectedFiles = Array.from(files).slice(0, 10); // 最多10个文件
            displayFileList();
            uploadBtn.style.display = selectedFiles.length > 0 ? 'block' : 'none';
        }
        
        // 显示文件列表
        function displayFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <div class="file-status status-waiting" id="status-${index}">等待上传</div>
                `;
                fileList.appendChild(fileItem);
            });
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 上传按钮事件
        uploadBtn.addEventListener('click', uploadFiles);
        
        // 批量上传文件
        async function uploadFiles() {
            if (selectedFiles.length === 0) return;
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            progressBar.style.display = 'block';
            results.style.display = 'none';
            
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });
            
            // 更新所有文件状态为上传中
            selectedFiles.forEach((_, index) => {
                const statusEl = document.getElementById(`status-${index}`);
                statusEl.className = 'file-status status-uploading';
                statusEl.textContent = '上传中';
            });
            
            try {
                const response = await fetch('/upload/batch', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResults(result);
                    updateFileStatuses(result);
                } else {
                    throw new Error(result.error || '上传失败');
                }
                
            } catch (error) {
                console.error('Upload error:', error);
                alert('上传失败: ' + error.message);
                
                // 更新所有文件状态为错误
                selectedFiles.forEach((_, index) => {
                    const statusEl = document.getElementById(`status-${index}`);
                    statusEl.className = 'file-status status-error';
                    statusEl.textContent = '上传失败';
                });
            }
            
            progressFill.style.width = '100%';
            uploadBtn.disabled = false;
            uploadBtn.textContent = '重新上传';
        }
        
        // 更新文件状态
        function updateFileStatuses(result) {
            // 标记成功的文件
            result.results.forEach((item, index) => {
                const statusEl = document.getElementById(`status-${index}`);
                if (statusEl) {
                    statusEl.className = 'file-status status-success';
                    statusEl.textContent = '上传成功';
                }
            });
            
            // 标记失败的文件
            if (result.errors) {
                result.errors.forEach((error, index) => {
                    const statusEl = document.getElementById(`status-${result.success + index}`);
                    if (statusEl) {
                        statusEl.className = 'file-status status-error';
                        statusEl.textContent = '上传失败';
                    }
                });
            }
        }
        
        // 显示上传结果
        function displayResults(result) {
            results.innerHTML = `
                <h3>上传完成</h3>
                <p>成功: ${result.success} 个，失败: ${result.failed} 个，总计: ${result.total} 个</p>
                ${result.results.map(item => `
                    <div class="result-item">
                        <span>${item.filename}</span>
                        <a href="${item.src}" target="_blank" class="result-link">查看文件</a>
                    </div>
                `).join('')}
                ${result.errors ? result.errors.map(error => `
                    <div class="result-item">
                        <span style="color: #dc3545;">${error.filename}: ${error.error}</span>
                    </div>
                `).join('') : ''}
            `;
            results.style.display = 'block';
        }
    </script>
</body>
</html>
