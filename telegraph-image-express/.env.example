# ===========================================
# Telegraph-Image Express Configuration
# ===========================================

# Telegram Bot Configuration (Required)
# Get your bot token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here
# Your Telegram channel/chat ID (use negative ID for channels)
TELEGRAM_CHAT_ID=your_chat_id_here

# Admin Authentication (Optional)
# If not set, admin panel will be accessible without authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# Server Configuration
PORT=3000
NODE_ENV=production

# Security Configuration (Optional)
# Comma-separated list of allowed origins for CORS
# ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Feature Flags
# Enable whitelist mode (only whitelisted images are accessible)
WHITELIST_MODE=false
# Disable telemetry and analytics
DISABLE_TELEMETRY=true

# Content Moderation (Optional)
# API key for content moderation service
# MODERATE_CONTENT_API_KEY=your_api_key_here

# File Upload Limits (Optional)
# Maximum file size in MB (default: 500MB, greatly relaxed)
# Maximum batch upload count: 100 files
# All file types are now allowed
# MAX_FILE_SIZE=500