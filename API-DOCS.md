# Telegraph-Image Express API 文档

## 📤 文件上传 API

### 1. 单文件上传

**接口地址**: `POST /upload`

**请求格式**: `multipart/form-data`

**参数**:
- `file`: 要上传的文件（必需）

**限制**:
- 文件大小: 最大 20MB
- 文件数量: 1个
- 支持格式: 图片、视频、音频、PDF、文本、ZIP

**响应格式**:
```json
[
  {
    "src": "/file/AgACAgUAAyEGAASk3CHlAAMOaFvumpKBuE4AAWle2vdamnCr0ovfAAIixzEbzCbhViLhwWIKyWqMAQADAgADeQADNgQ.png"
  }
]
```

**示例**:
```bash
curl -X POST \
  -F "file=@image.png" \
  http://localhost:3000/upload
```

### 2. 批量文件上传 🆕

**接口地址**: `POST /upload/batch`

**请求格式**: `multipart/form-data`

**参数**:
- `files`: 要上传的文件数组（必需）

**限制**:
- 文件大小: 每个文件最大 20MB
- 文件数量: 最多 10个
- 支持格式: 图片、视频、音频、PDF、文本、ZIP

**响应格式**:
```json
{
  "success": 2,
  "failed": 1,
  "total": 3,
  "results": [
    {
      "src": "/file/AgACAgUAAyEGAASk3CHlAAMOaFvumpKBuE4AAWle2vdamnCr0ovfAAIixzEbzCbhViLhwWIKyWqMAQADAgADeQADNgQ.png",
      "filename": "image1.png",
      "size": 108214
    },
    {
      "src": "/file/BQACAgUAAyEGAASk3CHlAAMPaFvumpKBuE4AAWle2vdamnCr0ovfAAIjxzEbzCbhViLhwWIKyWqMAQADAgADeQADNgQ.pdf",
      "filename": "document.pdf",
      "size": 245760
    }
  ],
  "errors": [
    {
      "filename": "large_file.mp4",
      "error": "File too large. Maximum size is 20MB per file."
    }
  ]
}
```

**示例**:
```bash
curl -X POST \
  -F "files=@image1.png" \
  -F "files=@image2.jpg" \
  -F "files=@document.pdf" \
  http://localhost:3000/upload/batch
```

**JavaScript 示例**:
```javascript
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);
formData.append('files', file3);

fetch('/upload/batch', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(result => {
  console.log(`成功上传: ${result.success} 个文件`);
  console.log(`失败: ${result.failed} 个文件`);
  
  result.results.forEach(item => {
    console.log(`文件链接: ${item.src}`);
  });
  
  if (result.errors) {
    result.errors.forEach(error => {
      console.error(`${error.filename}: ${error.error}`);
    });
  }
});
```

## 📁 文件访问 API

### 文件获取

**接口地址**: `GET /file/:id`

**参数**:
- `id`: 文件ID（包含扩展名）

**示例**:
```
http://localhost:3000/file/AgACAgUAAyEGAASk3CHlAAMOaFvumpKBuE4AAWle2vdamnCr0ovfAAIixzEbzCbhViLhwWIKyWqMAQADAgADeQADNgQ.png
```

## 🛠️ 管理 API

### 文件列表

**接口地址**: `GET /api/manage/list`

**认证**: Basic Auth（如果配置了管理员账户）

**查询参数**:
- `page`: 页码（默认: 1）
- `limit`: 每页数量（默认: 50）
- `search`: 搜索关键词
- `listType`: 过滤类型（White/Block/None）
- `sortBy`: 排序字段（默认: TimeStamp）
- `sortOrder`: 排序方向（asc/desc，默认: desc）

### 白名单管理

**接口地址**: `POST /api/manage/white/:id`

**认证**: Basic Auth

### 黑名单管理

**接口地址**: `POST /api/manage/block/:id`

**认证**: Basic Auth

### 删除文件

**接口地址**: `DELETE /api/manage/delete/:id`

**认证**: Basic Auth

## 📊 系统监控 API

### 健康检查

**接口地址**: `GET /health`

**响应格式**:
```json
{
  "status": "ok",
  "timestamp": "2025-06-25T12:40:47.810Z",
  "version": "1.0.0",
  "uptime": "1m 14s",
  "memory": {
    "used": "9MB",
    "total": "10MB"
  },
  "environment": "development",
  "features": {
    "telegramConfigured": true,
    "adminConfigured": true,
    "whitelistMode": false
  }
}
```

## 🔧 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 需要认证
- `404`: 文件不存在
- `413`: 文件过大
- `429`: 请求过于频繁
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "error": "错误描述信息"
}
```

## 📝 使用说明

### 支持的文件类型

- **所有文件类型**: 已移除文件类型限制，支持任意格式文件上传

### 文件大小限制

- 单个文件最大: 500MB（大幅放宽限制）
- 批量上传最多: 100个文件

### 访问控制

- **白名单模式**: 只有白名单中的文件可以访问
- **黑名单**: 黑名单中的文件会被阻止访问
- **成人内容**: 自动检测并阻止成人内容（需配置API密钥）

### 缓存策略

- 文件响应包含缓存头部
- 静态资源缓存1天
- 支持 ETag 和条件请求

## 🌐 Web 界面

- **主页**: `/` - 单文件上传界面
- **批量上传**: `/batch-upload.html` - 批量文件上传界面
- **管理后台**: `/admin.html` - 文件管理界面
- **健康检查**: `/health` - 系统状态页面
